/* 重置样式和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 全局图片优化 */
img {
    max-width: 100%;
    height: auto;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    image-rendering: auto;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* 顶部导航栏样式 */
.top-navbar {
    background: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid #e9ecef;
}

.navbar-container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 15px 60px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.company-logo {
    height: 50px;
    width: auto;
    object-fit: contain;
}

.company-name {
    color: #333333;
    font-size: 24px;
    font-weight: 600;
    letter-spacing: 1px;
}

/* 主容器 - 1440px版心 */
.container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 40px 60px;
    background-color: #ffffff;
    min-height: 100vh;
}

/* 标题区域 */
.header {
    margin-bottom: 30px;
}

.main-title {
    font-size: 32px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: left;
    letter-spacing: 1px;
}

.product-banner {
    background: linear-gradient(135deg, #3b4a6b 0%, #2c3e50 100%);
    padding: 20px 30px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-title {
    color: #ffffff;
    font-size: 24px;
    font-weight: 500;
    margin: 0;
    letter-spacing: 0.5px;
}

/* 产品描述区域 */
.product-description {
    margin: 30px 0;
    padding: 0 10px;
}

.description-text {
    font-size: 16px;
    line-height: 1.8;
    color: #555;
    text-align: justify;
    text-indent: 2em;
    letter-spacing: 0.3px;
}

/* 产品图片区域 */
.product-image {
    margin: 40px 0;
    text-align: center;
}

.image-container {
    display: inline-block;
    border-radius: 12px;
    overflow: hidden;
    /* box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12); */
    background: #fff;
    padding: 20px;
}

.main-image {
    width: 100%;
    max-width: 800px;
    height: auto;
    display: block;
    border-radius: 8px;
}

/* 产品特点区域 */
.product-features {
    margin-top: 50px;
}

.features-header {
    margin-bottom: 40px;
}

.features-title {
    background: linear-gradient(135deg, #3b4a6b 0%, #2c3e50 100%);
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
    padding: 15px 25px;
    border-radius: 8px;
    display: inline-block;
    margin: 0;
    letter-spacing: 0.5px;
}

.features-grid {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.feature-row {
    display: flex;
    justify-content: space-between;
    gap: 40px;
}

.feature-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 30px 25px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #3b4a6b;
}

.feature-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3b4a6b 0%, #2c3e50 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(59, 74, 107, 0.3);
}

.feature-icon svg {
    width: 32px;
    height: 32px;
    fill: #ffffff;
}

.feature-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    letter-spacing: 0.3px;
}

.feature-desc {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    text-align: left;
    letter-spacing: 0.2px;
}

/* 新增板块通用样式 */
.safety-section,
.convenience-section,
.intelligence-section {
    margin-top: 20px;
    padding: 20px 0;
}

.section-header {
    margin-bottom: 40px;
}

.section-title {
    background: linear-gradient(135deg, #3b4a6b 0%, #2c3e50 100%);
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
    padding: 15px 25px;
    border-radius: 8px;
    display: inline-block;
    margin: 0;
    letter-spacing: 0.5px;
}

/* 更安全板块样式 */
.safety-features {
    display: flex;
    justify-content: space-between;
    gap: 0;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.safety-feature {
    flex: 0 0 33.33%;
    padding: 25px 20px;
    /* border-right: 3px solid #3b4a6b; */
    position: relative;
}

.safety-feature:last-child {
    border-right: none;
}

.safety-feature:hover {
    background: #f8f9fa;
}

.feature-subtitle {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #3b4a6b;
    letter-spacing: 0.3px;
    position: relative;
}

.feature-text {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 20px;
    text-align: justify;
    letter-spacing: 0.2px;
}

.feature-image {
    text-align: center;
    margin-top: 20px;
}

.safety-img,
.convenience-img,
.intelligence-img {
    width: 100%;
    max-width: 280px;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

/* 更便捷板块样式 */
.convenience-features {
    display: flex;
    justify-content: space-between;
    gap: 0;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.convenience-feature {
    flex: 0 0 33.33%;
    padding: 25px 20px;
    /* border-right: 3px solid #3b4a6b; */
    position: relative;
}

.convenience-feature:last-child {
    border-right: none;
}

.convenience-feature:hover {
    background: #f8f9fa;
}

/* 更智能板块样式 */
.intelligence-features {
    display: flex;
    justify-content: space-between;
    gap: 0;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.intelligence-feature {
    flex: 0 0 33.33%;
    padding: 30px 25px;
    /* border-right: 3px solid #3b4a6b; */
    position: relative;
}

.intelligence-feature:last-child {
    border-right: none;
}

.intelligence-feature:hover {
    background: #f8f9fa;
}

/* 产品外观板块样式 */
.product-appearance-section {
    margin-top: 20px;
    padding: 20px 0;
    /* background: #f8f9fa; */
}

.product-showcase {
    display: flex;
    align-items: center;
    background: #e9ecef;
    border-radius: 8px;
    padding: 40px;
    margin-top: 30px;
}

.product-info {
    flex: 1;
    padding-right: 40px;
}

.product-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.play-icon {
    color: #3b4a6b;
    font-size: 20px;
    font-weight: bold;
}

.product-title h4 {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
    margin: 0;
}

.product-image {
    flex: 1;
    text-align: center;
}

.product-img {
    max-width: 100%;
    height: auto;
    max-height: 300px;
    border-radius: 8px;
}

/* 应用场景板块样式 */
.application-section {
    margin-top: 20px;
    padding: 20px 0;
}

.application-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 80px;
    margin-top: 30px;
}

.application-item {
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.application-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.application-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.app-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.application-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 20px 20px 15px 20px;
    line-height: 1.3;
}

.application-text {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin: 0 20px 20px 20px;
}

/* 产品规格板块样式 */
.specifications-section {
    margin-top: 20px;
    padding: 20px 0;
    background: #ffffff;
}

.specifications-table-container {
    margin-top: 30px;
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.specifications-table {
    width: 100%;
    border-collapse: collapse;
    background: #ffffff;
    font-size: 14px;
}

.specifications-table th,
.specifications-table td {
    padding: 12px 15px;
    text-align: left;
    border: 1px solid #e0e0e0;
}

.spec-header {
    background: #3b4a6b;
    color: #ffffff;
    font-weight: 600;
    text-align: center;
    font-size: 15px;
}

.model-header {
    background: #3b4a6b;
    color: #ffffff;
    font-weight: 600;
    text-align: center;
    min-width: 200px;
}

.spec-label {
    background: #f8f9fa;
    font-weight: 500;
    color: #2c3e50;
    text-align: center;
    min-width: 120px;
    border-right: 2px solid #3b4a6b;
}

.spec-value {
    background: #ffffff;
    color: #555;
    line-height: 1.5;
    text-align: center;
}

.spec-merged {
    background: #f8f9fa;
    font-weight: 500;
}

.specifications-table tbody tr:nth-child(even) .spec-value:not(.spec-merged) {
    background: #f8f9fa;
}

.specifications-table tbody tr:hover {
    background-color: rgba(59, 74, 107, 0.05);
}

.specifications-table tbody tr:hover .spec-label {
    background-color: rgba(59, 74, 107, 0.1);
}

.specifications-table tbody tr:hover .spec-value:not(.spec-merged) {
    background-color: rgba(59, 74, 107, 0.05);
}

/* 系统构成板块样式 */
.system-composition-section {
    margin-top: 20px;
    padding: 20px 0;
    background: #ffffff;
    width: 100%;
    display: block;
}

.system-components {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 40px;
    width: 100%;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.system-component {
    display: flex;
    align-items: flex-start;
    gap: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.system-component:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.component-image {
    flex-shrink: 0;
    width: 280px;
    height: 200px;
    background: #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    /* 图片容器优化 */
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
}

.component-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    transition: transform 0.3s ease;
    /* 图片清晰度优化 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
    image-rendering: auto;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    /* 抗锯齿优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 高DPI显示优化 */
    max-width: 100%;
    height: auto;
    display: block;
}

.system-component:hover .component-img {
    transform: scale(1.05);
}

.component-info {
    flex: 1;
    padding-left: 10px;
    text-align: center;
}

.component-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 20px;
}

.play-icon {
    color: #3b4a6b;
    font-size: 16px;
    font-weight: bold;
}

.component-title h4 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
}

.component-features {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: center;
}

.component-features li {
    padding: 8px 0;
    color: #555;
    font-size: 15px;
    line-height: 1.5;
    position: relative;
    padding-left: 0;
    text-align: center;
}

.component-features li:before {
    content: "•";
    color: #3b4a6b;
    font-weight: bold;
    margin-right: 8px;
}

/* 底部logo区域 */
.footer {
    margin-top: 60px;
    padding-top: 40px;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.logo-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.company-logo {
    display: flex;
    align-items: baseline;
    /* gap: 5px; */
    /* padding: 15px 25px; */
    /* background: linear-gradient(135deg, #3b4a6b 0%, #2c3e50 100%); */
    border-radius: 8px;
    /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); */
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
    letter-spacing: 1px;
}

.logo-subtext {
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
    opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 1440px) {
    .container {
        max-width: 100%;
        padding: 30px 40px;
    }
}

@media (max-width: 1024px) {
    .container {
        padding: 20px 30px;
    }

    .main-title {
        font-size: 28px;
    }

    .product-title {
        font-size: 20px;
    }

    .feature-row {
        flex-direction: column;
        gap: 20px;
    }

    .feature-item {
        padding: 25px 20px;
    }

    /* 新增板块响应式 */
    .safety-features,
    .convenience-features,
    .intelligence-features {
        flex-direction: column;
        gap: 0;
    }

    .safety-feature,
    .convenience-feature,
    .intelligence-feature {
        padding: 20px 15px;
        border-right: none;
        /* border-bottom: 2px solid #3b4a6b; */
    }

    .safety-feature:last-child,
    .convenience-feature:last-child,
    .intelligence-feature:last-child {
        border-bottom: none;
    }

    /* 产品外观响应式 */
    .product-showcase {
        flex-direction: column;
        text-align: center;
        padding: 30px 20px;
    }

    .product-info {
        padding-right: 0;
        margin-bottom: 30px;
    }

    .product-title {
        justify-content: center;
    }

    .product-title h4 {
        font-size: 20px;
    }

    /* 应用场景响应式 */
    .application-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* 产品规格响应式 */
    .specifications-table-container {
        margin-top: 20px;
    }

    .specifications-table {
        font-size: 13px;
    }

    .specifications-table th,
    .specifications-table td {
        padding: 10px 12px;
    }

    .spec-header {
        font-size: 14px;
    }

    .spec-label {
        min-width: 100px;
    }

    .model-header {
        min-width: 180px;
    }

    /* 产品规格响应式 */
    .specifications-table-container {
        margin-top: 20px;
    }

    .specifications-table {
        font-size: 13px;
    }

    .specifications-table th,
    .specifications-table td {
        padding: 10px 12px;
    }

    .spec-header {
        font-size: 14px;
    }

    .spec-label {
        min-width: 100px;
    }

    .model-header {
        min-width: 180px;
    }

    /* 系统构成响应式 */
    .system-components {
        gap: 30px;
    }

    .system-component {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
    }

    .component-image {
        width: 100%;
        height: 180px;
    }

    .component-info {
        padding-left: 0;
    }

    .component-title h4 {
        font-size: 18px;
    }

    .component-features li {
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px 20px;
    }

    .main-title {
        font-size: 24px;
    }

    .product-title {
        font-size: 18px;
    }

    .description-text {
        font-size: 14px;
        text-indent: 1.5em;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
    }

    .feature-icon svg {
        width: 28px;
        height: 28px;
    }

    .feature-title {
        font-size: 16px;
    }

    .feature-desc {
        font-size: 13px;
    }

    /* 新增板块移动端样式 */
    .section-title {
        font-size: 18px;
        padding: 12px 20px;
    }

    .feature-subtitle {
        font-size: 15px;
    }

    .feature-text {
        font-size: 13px;
    }

    .safety-img,
    .convenience-img,
    .intelligence-img {
        max-width: 200px;
    }

    /* 产品外观移动端样式 */
    .product-showcase {
        padding: 20px 15px;
    }

    .product-title h4 {
        font-size: 18px;
    }

    /* 应用场景移动端样式 */
    .application-image {
        height: 160px;
    }

    .application-title {
        font-size: 16px;
        margin: 15px 15px 10px 15px;
    }

    .application-text {
        font-size: 13px;
        margin: 0 15px 15px 15px;
    }

    /* 产品规格移动端样式 */
    .specifications-table-container {
        margin-top: 15px;
        border-radius: 6px;
    }

    .specifications-table {
        font-size: 12px;
    }

    .specifications-table th,
    .specifications-table td {
        padding: 8px 10px;
    }

    .spec-header {
        font-size: 13px;
    }

    .spec-label {
        min-width: 80px;
        font-size: 12px;
    }

    .model-header {
        min-width: 140px;
        font-size: 12px;
    }

    .spec-value {
        font-size: 11px;
        line-height: 1.4;
    }

    /* 系统构成移动端样式 */
    .system-composition-section {
        display: block !important;
        visibility: visible !important;
        margin-top: 15px;
        padding: 15px 0;
    }

    .system-components {
        gap: 25px;
        display: flex !important;
        flex-direction: column !important;
    }

    .system-component {
        padding: 15px;
        gap: 15px;
        display: flex !important;
        flex-direction: column !important;
    }

    .component-image {
        height: 160px;
        width: 100% !important;
    }

    .component-info {
        display: block !important;
    }

    .component-title h4 {
        font-size: 16px;
    }

    .component-features li {
        font-size: 13px;
        padding: 6px 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px 15px;
    }

    .main-title {
        font-size: 20px;
    }

    .product-banner {
        padding: 15px 20px;
    }

    .product-title {
        font-size: 16px;
    }

    .features-grid {
        gap: 20px;
    }

    .feature-item {
        padding: 20px 15px;
    }

    /* 新增板块小屏幕样式 */
    .safety-section,
    .convenience-section,
    .intelligence-section {
        margin-top: 40px;
        padding: 30px 0;
    }

    .section-title {
        font-size: 16px;
        padding: 10px 15px;
    }

    .feature-subtitle {
        font-size: 14px;
    }

    .feature-text {
        font-size: 12px;
        line-height: 1.5;
    }

    .safety-feature,
    .convenience-feature,
    .intelligence-feature {
        padding: 15px 12px;
    }

    .safety-img,
    .convenience-img,
    .intelligence-img {
        max-width: 180px;
    }

    /* 产品外观小屏幕样式 */
    .product-showcase {
        padding: 15px 10px;
    }

    .product-title h4 {
        font-size: 16px;
    }

    /* 应用场景小屏幕样式 */
    .application-grid {
        gap: 15px;
    }

    .application-image {
        height: 140px;
    }

    .application-title {
        font-size: 14px;
        margin: 12px 12px 8px 12px;
    }

    .application-text {
        font-size: 12px;
        margin: 0 12px 12px 12px;
        line-height: 1.5;
    }

    /* 产品规格小屏幕样式 */
    .specifications-table-container {
        margin-top: 10px;
        border-radius: 4px;
    }

    .specifications-table {
        font-size: 11px;
    }

    .specifications-table th,
    .specifications-table td {
        padding: 6px 8px;
    }

    .spec-header {
        font-size: 12px;
    }

    .spec-label {
        min-width: 70px;
        font-size: 11px;
    }

    .model-header {
        min-width: 120px;
        font-size: 11px;
    }

    .spec-value {
        font-size: 10px;
        line-height: 1.3;
    }

    /* 系统构成小屏幕样式 */
    .system-composition-section {
        display: block !important;
        visibility: visible !important;
        margin-top: 10px;
        padding: 10px 0;
    }

    .system-components {
        gap: 20px;
        display: flex !important;
        flex-direction: column !important;
    }

    .system-component {
        padding: 12px;
        gap: 12px;
        display: flex !important;
        flex-direction: column !important;
    }

    .component-image {
        height: 140px;
        width: 100% !important;
    }

    .component-info {
        display: block !important;
    }

    .component-title {
        gap: 8px;
        margin-bottom: 15px;
    }

    .component-title h4 {
        font-size: 14px;
    }

    .component-features li {
        font-size: 12px;
        padding: 5px 0;
    }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .component-img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: -moz-crisp-edges;
        image-rendering: crisp-edges;
        image-rendering: auto;
    }

    img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: auto;
    }
}
